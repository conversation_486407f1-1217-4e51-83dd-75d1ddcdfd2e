'use client';

import { AuthLayout } from '@/components/auth/auth-layout';

export default function SignUpSuccessPage() {
  return (
    <AuthLayout>
      <div className='w-full max-w-md'>
        <div className='text-center mb-8'>
          <h1 className='text-3xl font-bold text-white mb-2'>Mouvers</h1>
          <p className='text-gray-200'>
            ¡Registro exitoso!
          </p>
        </div>

        <div className='bg-white rounded-lg p-6 shadow-lg'>
          <div className='text-center'>
            <div className='mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4'>
              <svg
                className='h-6 w-6 text-green-600'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M5 13l4 4L19 7'
                />
              </svg>
            </div>
            
            <h3 className='text-lg font-medium text-gray-900 mb-2'>
              ¡Cuenta creada exitosamente!
            </h3>
            
            <p className='text-sm text-gray-600 mb-6'>
              Tu cuenta ha sido creada. Ahora puedes iniciar sesión para acceder a tu dashboard.
            </p>

            <a
              href='/auth/login'
              className='w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors'
            >
              Ir al Login
            </a>
          </div>
        </div>
      </div>
    </AuthLayout>
  );
}
