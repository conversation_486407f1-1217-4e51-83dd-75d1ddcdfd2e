'use client';

import { useEffect, useState, useCallback, useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix for Leaflet icons in Next.js
if (typeof window !== 'undefined') {
  delete (L.Icon.Default.prototype as unknown as Record<string, unknown>)
    ._getIconUrl;
  L.Icon.Default.mergeOptions({
    iconRetinaUrl:
      'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
    iconUrl:
      'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
    shadowUrl:
      'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
  });
}

interface DeliveryMapProps {
  pickupAddress?:
    | {
        street?: string;
        number?: string;
        city?: string;
        state?: string;
        zip?: string;
        lat?: number;
        lng?: number;
        name?: string;
        coordinates?: { lat: number; lng: number } | [number, number];
      }
    | string;
  deliveryAddress?:
    | {
        street?: string;
        number?: string;
        city?: string;
        state?: string;
        zip?: string;
        lat?: number;
        lng?: number;
        name?: string;
        coordinates?: { lat: number; lng: number } | [number, number];
      }
    | string;
  className?: string;
}

export default function DeliveryMap({
  pickupAddress,
  deliveryAddress,
  className = 'h-96',
}: DeliveryMapProps) {
  const [route, setRoute] = useState<[number, number][]>([]);
  const [distance, setDistance] = useState<number>(0);
  const [loading, setLoading] = useState(false);

  // Coordenadas de ejemplo (Monterrey, México)
  const defaultPickup = {
    lat: 25.6866,
    lng: -100.3161,
    name: 'Punto de Recogida',
  };
  const defaultDelivery = {
    lat: 25.6595,
    lng: -100.3624,
    name: 'Punto de Entrega',
  };

  // Función para extraer coordenadas de las direcciones
  const extractCoordinates = (address: unknown) => {
    if (!address) return null;

    // Si es un string, no podemos extraer coordenadas
    if (typeof address === 'string') return null;

    // Verificar que es un objeto
    if (typeof address !== 'object' || address === null) return null;

    const addr = address as Record<string, unknown>;

    // Si ya tiene lat/lng, usarlas
    if (addr.lat && addr.lng) {
      return {
        lat: parseFloat(addr.lat.toString()),
        lng: parseFloat(addr.lng.toString()),
        name: (addr.street as string) || 'Dirección',
      };
    }

    // Si tiene coordenadas en otro formato
    if (addr.coordinates) {
      let lat: number, lng: number;

      if (Array.isArray(addr.coordinates)) {
        // Formato [lng, lat]
        lat = parseFloat(addr.coordinates[1]?.toString() || '0');
        lng = parseFloat(addr.coordinates[0]?.toString() || '0');
      } else {
        // Formato { lat, lng }
        const coords = addr.coordinates as {
          lat?: number | string;
          lng?: number | string;
        };
        lat = parseFloat(coords.lat?.toString() || '0');
        lng = parseFloat(coords.lng?.toString() || '0');
      }

      return {
        lat,
        lng,
        name: (addr.street as string) || 'Dirección',
      };
    }

    return null;
  };

  const pickupCoords = extractCoordinates(pickupAddress);
  const deliveryCoords = extractCoordinates(deliveryAddress);

  const pickup = pickupCoords || defaultPickup;
  const delivery = deliveryCoords || defaultDelivery;

  // Memoize coordinates to prevent unnecessary recalculations
  const coordinates = useMemo(
    () => ({
      pickup: { lat: pickup.lat, lng: pickup.lng },
      delivery: { lat: delivery.lat, lng: delivery.lng },
    }),
    [pickup.lat, pickup.lng, delivery.lat, delivery.lng]
  );

  const center: [number, number] = [
    (pickup.lat + delivery.lat) / 2,
    (pickup.lng + delivery.lng) / 2,
  ];

  // Calcular ruta usando la API de OSRM (gratuita)
  const calculateRoute = useCallback(async () => {
    // Validar que tenemos coordenadas válidas
    if (
      !coordinates.pickup.lat ||
      !coordinates.pickup.lng ||
      !coordinates.delivery.lat ||
      !coordinates.delivery.lng
    ) {
      console.warn('Coordenadas inválidas:', {
        pickup: coordinates.pickup,
        delivery: coordinates.delivery,
      });
      setLoading(false);
      return;
    }

    setLoading(true);
    console.log(
      '🔄 Calculando ruta entre:',
      coordinates.pickup,
      'y',
      coordinates.delivery
    );

    // Check if we should skip API calls (if previous attempts failed consistently)
    const skipApiKey = 'osrm-api-failed';
    const failedTime = sessionStorage.getItem('osrm-api-failed-time');
    const shouldSkipApi = sessionStorage.getItem(skipApiKey) === 'true';

    // Reset API failure flag after 1 hour
    if (shouldSkipApi && failedTime) {
      const failedTimestamp = parseInt(failedTime);
      const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds

      if (Date.now() - failedTimestamp > oneHour) {
        sessionStorage.removeItem(skipApiKey);
        sessionStorage.removeItem('osrm-api-failed-time');
        console.log('🔄 API failure flag expirado, reintentando...');
      }
    }

    if (shouldSkipApi) {
      console.log(
        '🚫 Saltando API - fallo previo detectado, usando ruta en línea recta'
      );
      setRoute([
        [coordinates.pickup.lat, coordinates.pickup.lng],
        [coordinates.delivery.lat, coordinates.delivery.lng],
      ]);
      setDistance(
        calculateDirectDistance(coordinates.pickup, coordinates.delivery)
      );
      setLoading(false);
      return;
    }

    // Retry mechanism with exponential backoff
    const maxRetries = 1; // Reduced retries for faster fallback
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          // Wait before retry with exponential backoff
          const delay = Math.pow(2, attempt) * 1000; // 1s, 2s, 4s
          console.log(`🔄 Reintento ${attempt}, esperando ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        // Create AbortController for timeout - reduced to 5 seconds for faster fallback
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        try {
          const response = await fetch(
            `https://router.project-osrm.org/route/v1/driving/${coordinates.pickup.lng},${coordinates.pickup.lat};${coordinates.delivery.lng},${coordinates.delivery.lat}?overview=full&geometries=geojson`,
            {
              signal: controller.signal,
            }
          );

          clearTimeout(timeoutId); // Clear timeout if request succeeds

          // Check if response is rate limited
          if (response.status === 429) {
            if (attempt < maxRetries) {
              console.log(
                `⚠️ Rate limit alcanzado, reintentando en ${Math.pow(2, attempt + 1)}s...`
              );
              continue;
            } else {
              console.log(
                '⚠️ Rate limit persistente, usando ruta en línea recta'
              );
              throw new Error('Rate limit exceeded');
            }
          }

          // Check if response is not JSON
          const contentType = response.headers.get('content-type');
          if (!contentType || !contentType.includes('application/json')) {
            if (attempt < maxRetries) {
              console.log(`⚠️ Respuesta no es JSON, reintentando...`);
              continue;
            } else {
              console.log(
                '⚠️ Respuesta no es JSON, usando ruta en línea recta'
              );
              throw new Error('Invalid response format');
            }
          }

          const data = await response.json();

          if (data.routes && data.routes[0]) {
            const routeData = data.routes[0];
            setRoute(
              routeData.geometry.coordinates.map((coord: [number, number]) => [
                coord[1],
                coord[0],
              ])
            );
            setDistance(routeData.distance / 1000); // Convertir a km
            console.log('✅ Ruta calculada exitosamente');
            setLoading(false);
            return; // Success, exit the function
          } else {
            throw new Error('No route data available');
          }
        } catch (error) {
          clearTimeout(timeoutId); // Clear timeout on error

          // Handle specific error types
          if (error instanceof Error) {
            if (error.name === 'AbortError') {
              lastError = new Error('Request timeout - API no responde');
              console.log('⏰ Timeout alcanzado, API no responde');
            } else if (error.message.includes('Failed to fetch')) {
              lastError = new Error('Error de conexión - API no disponible');
              console.log('🌐 Error de conexión, API no disponible');
            } else {
              lastError = error;
            }
          } else {
            lastError = new Error('Unknown error');
          }

          if (attempt < maxRetries) {
            console.log(
              `❌ Error en intento ${attempt + 1}:`,
              lastError.message
            );
            continue;
          }
        }
      } catch (error) {
        // Handle any other errors from the outer try block
        lastError = error instanceof Error ? error : new Error('Unknown error');

        if (attempt < maxRetries) {
          console.log(
            `❌ Error general en intento ${attempt + 1}:`,
            lastError.message
          );
          continue;
        }
      }
    }

    // If we get here, all retries failed
    console.log('🔄 Usando ruta en línea recta como fallback');

    // Mark API as failed for future requests (valid for 1 hour)
    if (
      lastError &&
      (lastError.message.includes('timeout') ||
        lastError.message.includes('conexión'))
    ) {
      try {
        sessionStorage.setItem(skipApiKey, 'true');
        sessionStorage.setItem('osrm-api-failed-time', Date.now().toString());
        console.log(
          '🚫 API marcada como fallida - saltando futuras llamadas por 1 hora'
        );
      } catch (storageError) {
        // Log non-fatal warning but continue flow
        console.warn(
          '⚠️ No se pudo guardar estado de API en sessionStorage:',
          storageError
        );
      }
    }

    // Ruta en línea recta como fallback
    setRoute([
      [coordinates.pickup.lat, coordinates.pickup.lng],
      [coordinates.delivery.lat, coordinates.delivery.lng],
    ]);
    setDistance(
      calculateDirectDistance(coordinates.pickup, coordinates.delivery)
    );

    // Log the specific error for debugging
    if (lastError) {
      console.log(`ℹ️ Motivo del fallback: ${lastError.message}`);
    }

    setLoading(false);
  }, [coordinates]);

  // Calcular distancia en línea recta
  const calculateDirectDistance = (
    point1: { lat: number; lng: number },
    point2: { lat: number; lng: number }
  ) => {
    const R = 6371; // Radio de la Tierra en km
    const dLat = ((point2.lat - point1.lat) * Math.PI) / 180;
    const dLng = ((point2.lng - point1.lng) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((point1.lat * Math.PI) / 180) *
        Math.cos((point2.lat * Math.PI) / 180) *
        Math.sin(dLng / 2) *
        Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  };

  useEffect(() => {
    console.log('🗺️ DeliveryMap recibió direcciones:', {
      pickupAddress,
      deliveryAddress,
    });

    // Only calculate route if we have valid coordinates
    if (pickup.lat && pickup.lng && delivery.lat && delivery.lng) {
      calculateRoute();
    }
  }, [
    coordinates,
    pickup.lat,
    pickup.lng,
    delivery.lat,
    delivery.lng,
    deliveryAddress,
    pickupAddress,
    calculateRoute,
  ]);

  // Cleanup effect to prevent map container conflicts
  useEffect(() => {
    return () => {
      // Clean up any existing map instances when component unmounts
      if (typeof window !== 'undefined') {
        const mapContainers = document.querySelectorAll('.leaflet-container');
        mapContainers.forEach(container => {
          const leafletContainer = container as HTMLElement & {
            _leaflet_id?: number;
          };
          if (leafletContainer._leaflet_id) {
            // Remove the leaflet instance
            leafletContainer._leaflet_id = undefined;
          }
        });
      }
    };
  }, []);

  // Verificar si tenemos coordenadas válidas
  const hasValidCoordinates =
    pickup.lat && pickup.lng && delivery.lat && delivery.lng;

  // Verificar que estamos en el cliente después de los hooks
  if (typeof window === 'undefined') {
    return (
      <div className={`w-full ${className}`}>
        <div className='bg-gray-100 rounded-lg flex items-center justify-center h-full'>
          <p className='text-gray-600'>Cargando mapa...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      <div className='mb-4 p-4 bg-white rounded-lg shadow-sm border border-gray-200'>
        <h3 className='text-lg font-semibold text-gray-900 mb-2'>
          Ruta de Entrega
        </h3>

        {!hasValidCoordinates ? (
          <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-3'>
            <p className='text-yellow-800 text-sm'>
              ⚠️ No se pudieron obtener las coordenadas de las direcciones.
              Mostrando ubicación de ejemplo.
            </p>
          </div>
        ) : (
          <div className='grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm'>
            <div>
              <span className='font-medium text-gray-700'>Distancia:</span>
              <p className='text-gray-900 font-semibold'>
                {distance.toFixed(2)} km
              </p>
            </div>
            <div>
              <span className='font-medium text-gray-700'>Estado:</span>
              <p className='text-gray-900'>
                {loading ? 'Calculando...' : 'Ruta calculada'}
              </p>
            </div>
          </div>
        )}
      </div>

      <div className='bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden'>
        <div className='w-full h-80' style={{ height: '320px' }}>
          <MapContainer
            center={center}
            zoom={13}
            className='w-full h-full'
            id={`delivery-map-${Date.now()}`}
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url='https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
            />

            {/* Marcador de recogida */}
            <Marker position={[pickup.lat, pickup.lng]}>
              <Popup>
                <div className='text-center'>
                  <div className='font-semibold text-green-600'>
                    📍 Punto de Recogida
                  </div>
                  <div className='text-sm text-gray-600'>
                    {pickup.name || 'Punto de Recogida'}
                  </div>
                </div>
              </Popup>
            </Marker>

            {/* Marcador de entrega */}
            <Marker position={[delivery.lat, delivery.lng]}>
              <Popup>
                <div className='text-center'>
                  <div className='font-semibold text-red-600'>
                    🎯 Punto de Entrega
                  </div>
                  <div className='text-sm text-gray-600'>
                    {delivery.name || 'Punto de Entrega'}
                  </div>
                </div>
              </Popup>
            </Marker>

            {/* Línea de ruta */}
            {route.length > 0 && (
              <Polyline
                positions={route}
                color='#3B82F6'
                weight={4}
                opacity={0.8}
              />
            )}
          </MapContainer>
        </div>
      </div>
    </div>
  );
}
