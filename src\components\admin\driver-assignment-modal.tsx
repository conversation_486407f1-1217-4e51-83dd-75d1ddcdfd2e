'use client';

import { useState, useEffect } from 'react';
import { Di<PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Truck, User, Phone, Mail, Package, AlertCircle } from 'lucide-react';

interface Driver {
  id: string;
  name: string;
  email: string;
  phone: string;
  activeOrders: number;
  isActive: boolean;
  joinedDate: string;
}

interface Order {
  id: string;
  customer_name: string;
  pickup_address: {
    street_address: string;
    city: string;
    state: string;
  };
  delivery_addresses: {
    street_address: string;
    city: string;
    state: string;
  };
  total_cost: number;
}

interface DriverAssignmentModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  onAssignDriver: (orderId: string, driverId: string) => Promise<void>;
}

export function DriverAssignmentModal({
  order,
  isOpen,
  onClose,
  onAssignDriver,
}: DriverAssignmentModalProps) {
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [loading, setLoading] = useState(false);
  const [assigning, setAssigning] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      fetchAvailableDrivers();
    }
  }, [isOpen]);

  const fetchAvailableDrivers = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/drivers/available');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Error al cargar repartidores');
      }

      setDrivers(data.drivers || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error al cargar repartidores');
    } finally {
      setLoading(false);
    }
  };

  const handleAssignDriver = async (driverId: string) => {
    if (!order) return;

    setAssigning(driverId);
    try {
      await onAssignDriver(order.id, driverId);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error al asignar repartidor');
    } finally {
      setAssigning(null);
    }
  };

  if (!order) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Truck className="w-5 h-5" />
            Asignar Repartidor
          </DialogTitle>
          <DialogDescription>
            Selecciona un repartidor para la orden #{order.id.slice(-8)}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Summary */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <Package className="w-4 h-4" />
                <h3 className="font-medium">Resumen de la Orden</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">Cliente:</p>
                  <p className="font-medium">{order.customer_name}</p>
                </div>
                <div>
                  <p className="text-gray-600">Total:</p>
                  <p className="font-medium text-green-600">${order.total_cost?.toFixed(2)} MXN</p>
                </div>
                <div>
                  <p className="text-gray-600">Recoger en:</p>
                  <p className="font-medium">
                    {order.pickup_address.street_address}, {order.pickup_address.city}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600">Entregar en:</p>
                  <p className="font-medium">
                    {order.delivery_addresses.street_address}, {order.delivery_addresses.city}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Error Display */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
              <AlertCircle className="w-4 h-4 text-red-600" />
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          {/* Available Drivers */}
          <div>
            <h3 className="font-medium mb-4">Repartidores Disponibles</h3>
            
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : drivers.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Truck className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                <p>No hay repartidores disponibles</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {drivers.map((driver) => (
                  <Card key={driver.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-gray-600" />
                          <h4 className="font-medium">{driver.name}</h4>
                        </div>
                        <Badge 
                          variant={driver.activeOrders === 0 ? "default" : "secondary"}
                          className={driver.activeOrders === 0 ? "bg-green-100 text-green-800" : ""}
                        >
                          {driver.activeOrders} órdenes activas
                        </Badge>
                      </div>
                      
                      <div className="space-y-2 text-sm text-gray-600 mb-4">
                        <div className="flex items-center gap-2">
                          <Mail className="w-3 h-3" />
                          <span>{driver.email}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="w-3 h-3" />
                          <span>{driver.phone}</span>
                        </div>
                      </div>

                      <Button
                        onClick={() => handleAssignDriver(driver.id)}
                        disabled={assigning === driver.id}
                        className="w-full"
                        size="sm"
                      >
                        {assigning === driver.id ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Asignando...
                          </>
                        ) : (
                          'Asignar Repartidor'
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
