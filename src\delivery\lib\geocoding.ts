// Servicio de geocodificación usando OpenStreetMap Nominatim (gratuito)

export interface GeocodingResult {
  lat: number;
  lng: number;
  display_name: string;
  success: boolean;
  error?: string;
}

export async function geocodeAddress(
  address: string
): Promise<GeocodingResult> {
  try {
    // Construir la dirección completa
    const fullAddress = `${address}, México`;

    // URL de la API de Nominatim
    const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(fullAddress)}&limit=1&countrycodes=mx`;

    console.log('🗺️ Geocodificando dirección:', fullAddress);

    const response = await fetch(url, {
      headers: {
        'User-Agent': 'MouversRepartidor/1.0', // Requerido por Nominatim
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data && data.length > 0) {
      const result = data[0];
      console.log('✅ Coordenadas encontradas:', result);

      return {
        lat: parseFloat(result.lat),
        lng: parseFloat(result.lon),
        display_name: result.display_name,
        success: true,
      };
    } else {
      console.warn('⚠️ No se encontraron coordenadas para:', address);
      return {
        lat: 0,
        lng: 0,
        display_name: address,
        success: false,
        error: 'No se encontraron coordenadas',
      };
    }
  } catch (error) {
    console.error('❌ Error en geocodificación:', error);
    return {
      lat: 0,
      lng: 0,
      display_name: address,
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido',
    };
  }
}

// Función para geocodificar múltiples direcciones
export async function geocodeAddresses(
  addresses: string[]
): Promise<GeocodingResult[]> {
  const results: GeocodingResult[] = [];

  for (const address of addresses) {
    // Agregar delay para respetar límites de la API
    if (results.length > 0) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // 1 segundo entre requests
    }

    const result = await geocodeAddress(address);
    results.push(result);
  }

  return results;
}
