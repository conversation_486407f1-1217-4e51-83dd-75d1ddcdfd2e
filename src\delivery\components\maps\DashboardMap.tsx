'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix para los iconos de Leaflet en Next.js
delete (L.Icon.Default.prototype as unknown as Record<string, unknown>)
  ._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface DashboardMapProps {
  className?: string;
}

export default function DashboardMap({
  className = 'h-64',
}: DashboardMapProps) {
  // Coordenadas de ejemplo (Monterrey, México)
  const center = { lat: 25.6866, lng: -100.3161 };

  // Puntos de ejemplo (puedes reemplazar con datos reales)
  const deliveryPoints = [
    {
      lat: 25.6866,
      lng: -100.3161,
      name: 'Centro de Distribución',
      type: 'pickup',
    },
    { lat: 25.6595, lng: -100.3624, name: 'Entrega 1', type: 'delivery' },
    { lat: 25.7, lng: -100.3, name: 'Entrega 2', type: 'delivery' },
    { lat: 25.65, lng: -100.35, name: 'Entrega 3', type: 'delivery' },
  ];

  return (
    <div className={`w-full ${className}`}>
      <div className='bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden'>
        <MapContainer
          center={[center.lat, center.lng]}
          zoom={12}
          className='w-full h-full'
          style={{ height: '256px' }}
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url='https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
          />

          {/* Marcadores de puntos de entrega */}
          {deliveryPoints.map((point, index) => (
            <Marker key={index} position={[point.lat, point.lng]}>
              <Popup>
                <div className='text-center'>
                  <div
                    className={`font-semibold ${
                      point.type === 'pickup'
                        ? 'text-green-600'
                        : 'text-blue-600'
                    }`}
                  >
                    {point.type === 'pickup' ? '🏢' : '🎯'} {point.name}
                  </div>
                  <div className='text-xs text-gray-500 mt-1'>
                    {point.lat.toFixed(4)}, {point.lng.toFixed(4)}
                  </div>
                </div>
              </Popup>
            </Marker>
          ))}

          {/* Círculo de área de cobertura */}
          <Circle
            center={[center.lat, center.lng]}
            radius={5000} // 5km
            color='#3B82F6'
            fillColor='#3B82F6'
            fillOpacity={0.1}
            weight={2}
          />
        </MapContainer>
      </div>
    </div>
  );
}
