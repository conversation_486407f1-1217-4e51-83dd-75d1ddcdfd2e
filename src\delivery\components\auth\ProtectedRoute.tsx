'use client';

import { useAuthStore } from '@/stores/authStore';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Spinner } from '@/components/ui/spinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string;
}

export default function ProtectedRoute({
  children,
  requiredRole,
}: ProtectedRouteProps) {
  const { user, profile, loading, isDelivery } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
    }

    if (!loading && user && requiredRole && profile?.role !== requiredRole) {
      router.push('/auth/login');
    }

    // Redirect delivery users to their app if they try to access main app
    if (!loading && user && profile && isDelivery && !requiredRole) {
      router.push('/delivery');
    }
  }, [user, profile, loading, requiredRole, isDelivery, router]);

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-50'>
        <div className='text-center'>
          <Spinner className='mx-auto mb-4' />
          <p className='text-gray-600'>Verificando autenticación...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  if (requiredRole && profile?.role !== requiredRole) {
    return null;
  }

  // Redirect delivery users to their app
  if (isDelivery && !requiredRole) {
    router.push('/delivery');
    return null;
  }

  return <>{children}</>;
}
