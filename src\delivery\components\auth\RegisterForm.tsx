'use client';

import { useState } from 'react';
import { useAuthStore } from '@/stores/authStore';

export default function RegisterForm() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');
  const { signUp } = useAuthStore();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const validateForm = () => {
    if (formData.password !== formData.confirmPassword) {
      setError('Las contraseñas no coinciden');
      return false;
    }
    if (formData.password.length < 6) {
      setError('La contraseña debe tener al menos 6 caracteres');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    if (!validateForm()) {
      setLoading(false);
      return;
    }

    const { error } = await signUp(formData.email, formData.password);

    if (error) {
      setError(error.message || 'Error al registrarse');
    } else {
      setSuccess(
        '¡Registro exitoso! Revisa tu correo para confirmar tu cuenta.'
      );
      setFormData({
        email: '',
        password: '',
        confirmPassword: '',
      });
    }

    setLoading(false);
  };

  return (
    <div className='w-full'>
      <div className='text-center mb-6'>
        <h2 className='text-2xl font-bold text-white mb-2'>
          Registrarse como Repartidor
        </h2>
        <p className='text-gray-200'>
          Crea tu cuenta para acceder al sistema de repartidores
        </p>
      </div>

      <form className='space-y-6' onSubmit={handleSubmit}>
        <div className='space-y-4'>
          <div>
            <label
              htmlFor='email'
              className='block text-sm font-medium text-white mb-2'
            >
              Correo Electrónico
            </label>
            <input
              id='email'
              name='email'
              type='email'
              autoComplete='email'
              required
              className='w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all'
              placeholder='<EMAIL>'
              value={formData.email}
              onChange={handleChange}
            />
          </div>

          <div>
            <label
              htmlFor='password'
              className='block text-sm font-medium text-white mb-2'
            >
              Contraseña
            </label>
            <input
              id='password'
              name='password'
              type='password'
              autoComplete='new-password'
              required
              className='w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all'
              placeholder='Mínimo 6 caracteres'
              value={formData.password}
              onChange={handleChange}
            />
          </div>

          <div>
            <label
              htmlFor='confirmPassword'
              className='block text-sm font-medium text-white mb-2'
            >
              Confirmar Contraseña
            </label>
            <input
              id='confirmPassword'
              name='confirmPassword'
              type='password'
              autoComplete='new-password'
              required
              className='w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all'
              placeholder='Repite tu contraseña'
              value={formData.confirmPassword}
              onChange={handleChange}
            />
          </div>
        </div>

        {error && (
          <div className='rounded-lg bg-red-500/20 border border-red-400/30 p-4'>
            <div className='text-sm text-red-200'>{error}</div>
          </div>
        )}

        {success && (
          <div className='rounded-lg bg-green-500/20 border border-green-400/30 p-4'>
            <div className='text-sm text-green-200'>{success}</div>
          </div>
        )}

        <div>
          <button
            type='submit'
            disabled={loading}
            className='w-full py-3 px-4 bg-gradient-to-r from-blue-500 to-green-500 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-green-600 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-105'
          >
            {loading ? 'Creando cuenta...' : 'Crear Cuenta'}
          </button>
        </div>
      </form>
    </div>
  );
}
