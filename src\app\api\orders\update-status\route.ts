import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET() {
  return NextResponse.json({
    message: 'API endpoint is working',
    timestamp: new Date().toISOString(),
    env: {
      hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasSupabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    },
  });
}

export async function PUT(request: NextRequest) {
  try {
    // Check environment variables
    if (
      !process.env.NEXT_PUBLIC_SUPABASE_URL ||
      !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    ) {
      return NextResponse.json(
        { error: 'Error de configuración del servidor' },
        { status: 500 }
      );
    }

    let supabase;
    try {
      supabase = await createClient();
    } catch {
      return NextResponse.json(
        { error: 'Error al inicializar la conexión a la base de datos' },
        { status: 500 }
      );
    }

    // Check for Authorization header
    const authHeader = request.headers.get('authorization');
    let user = null;
    let authError = null;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      // Use the access token from the Authorization header
      const token = authHeader.substring(7);
      const { data, error } = await supabase.auth.getUser(token);
      user = data.user;
      authError = error;
    } else {
      // Fallback to session-based authentication
      const { data, error } = await supabase.auth.getUser();
      user = data.user;
      authError = error;
    }

    if (authError) {
      console.error('Auth error:', authError);
      return NextResponse.json(
        {
          error: 'Error de autenticación',
          details: authError.message,
        },
        { status: 401 }
      );
    }

    if (!user) {
      console.error('No user found in session');
      return NextResponse.json(
        {
          error: 'No autorizado - sesión no encontrada',
          details: 'Usuario no autenticado',
        },
        { status: 401 }
      );
    }

    // Get user profile to check role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Profile error:', profileError);
      return NextResponse.json(
        {
          error: 'Error al obtener perfil de usuario',
          details: profileError.message,
          code: profileError.code,
        },
        { status: 500 }
      );
    }

    if (!profile) {
      console.error('No profile found for user:', user.id);
      return NextResponse.json(
        { error: 'Perfil no encontrado' },
        { status: 404 }
      );
    }

    // Only allow delivery personnel and admins to update order statuses
    if (profile.role !== 'delivery' && profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'Permisos insuficientes' },
        { status: 403 }
      );
    }

    let requestBody;
    try {
      requestBody = await request.json();
    } catch {
      return NextResponse.json(
        { error: 'Formato de cuerpo de solicitud inválido' },
        { status: 400 }
      );
    }

    const { orderId } = requestBody;
    const newStatus = requestBody.newStatus;

    if (!orderId || !newStatus) {
      return NextResponse.json(
        { error: 'Se requiere el ID de la orden y el nuevo estado' },
        { status: 400 }
      );
    }

    // Validate UUID format for orderId
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(orderId)) {
      return NextResponse.json(
        { error: 'Formato de ID de orden inválido' },
        { status: 400 }
      );
    }

    // Validate status values
    const validStatuses = [
      'pending',
      'confirmed',
      'in-transit',
      'pending-admin-confirmation',
      'delivered',
      'closed',
      'cancelled',
    ];
    if (!validStatuses.includes(newStatus)) {
      return NextResponse.json(
        { error: 'Valor de estado inválido' },
        { status: 400 }
      );
    }

    // Only admins can close orders
    if (newStatus === 'closed' && profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'Solo los administradores pueden cerrar órdenes' },
        { status: 403 }
      );
    }

    // If delivery personnel is trying to mark as delivered, change to pending-admin-confirmation
    let finalStatus = newStatus;
    if (profile.role === 'delivery' && newStatus === 'delivered') {
      finalStatus = 'pending-admin-confirmation';
    }

    // If user is delivery personnel, check if they are assigned to this order
    if (profile.role === 'delivery') {
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .select('delivery_id, status')
        .eq('id', orderId)
        .single();

      if (orderError || !order) {
        return NextResponse.json(
          { error: 'Orden no encontrada' },
          { status: 404 }
        );
      }

      if (order.delivery_id !== user.id) {
        return NextResponse.json(
          { error: 'Solo puedes actualizar órdenes asignadas a ti' },
          { status: 403 }
        );
      }

      // Security: Prevent delivery personnel from modifying admin-confirmed orders
      if (order.status === 'confirmed' || order.status === 'in-transit') {
        // Only allow delivery personnel to mark as delivered (which becomes pending-admin-confirmation)
        if (newStatus !== 'pending-admin-confirmation') {
          return NextResponse.json(
            {
              error:
                'Por seguridad, no puedes modificar órdenes confirmadas por administración. Solo puedes marcarlas como entregadas.',
              currentStatus: order.status,
              allowedStatus: 'pending-admin-confirmation',
            },
            { status: 403 }
          );
        }
      }
    }

    // Additional validation for closing orders
    if (finalStatus === 'closed') {
      const { data: orderToClose, error: orderError } = await supabase
        .from('orders')
        .select('status')
        .eq('id', orderId)
        .single();

      if (orderError || !orderToClose) {
        return NextResponse.json(
          { error: 'Orden no encontrada' },
          { status: 404 }
        );
      }

      if (orderToClose.status !== 'delivered') {
        return NextResponse.json(
          {
            error: 'Solo se pueden cerrar órdenes que han sido entregadas',
            currentStatus: orderToClose.status,
            requiredStatus: 'delivered',
          },
          { status: 400 }
        );
      }
    }

    // Update the order status
    const { data: updateData, error: updateError } = await supabase
      .from('orders')
      .update({
        status: finalStatus,
        updated_at: new Date().toISOString(),
      })
      .eq('id', orderId)
      .select();

    if (updateError) {
      console.error('Database update error:', updateError);
      return NextResponse.json(
        {
          error: 'Error al actualizar el estado de la orden',
          details: updateError.message,
          code: updateError.code,
          hint: updateError.hint,
        },
        { status: 500 }
      );
    }

    // Check if any rows were actually updated
    if (!updateData || updateData.length === 0) {
      return NextResponse.json(
        { error: 'No se encontró la orden para actualizar' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Estado de la orden actualizado a ${finalStatus}`,
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido',
      },
      { status: 500 }
    );
  }
}
