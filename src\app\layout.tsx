import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

import { MobileNav } from '@/components/layout/mobile-nav';
import { Analytics } from '@vercel/analytics/next';
import { AuthProvider } from '@/contexts/AuthContext';

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'Mouvers - Your Trusted Delivery Platform',
  description: 'Delivery and logistics management platform for Mexico',
  keywords: ['delivery', 'logistics', 'Mexico', 'mouvers', 'shipping'],
  authors: [{ name: 'Mouvers Team' }],
  creator: 'Mouvers',
  publisher: 'Mouvers',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='en'>
      <body
        suppressHydrationWarning
        className={`${inter.className} antialiased`}
      >
        <Analytics />
        <AuthProvider>{children}</AuthProvider>
        <MobileNav />
      </body>
    </html>
  );
}
