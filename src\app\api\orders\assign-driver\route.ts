import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // Check environment variables
    if (
      !process.env.NEXT_PUBLIC_SUPABASE_URL ||
      !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    ) {
      return NextResponse.json(
        { error: 'Error de configuración del servidor' },
        { status: 500 }
      );
    }

    let supabase;
    try {
      supabase = await createClient();
    } catch {
      return NextResponse.json(
        { error: 'Error al inicializar la conexión a la base de datos' },
        { status: 500 }
      );
    }

    // Get the current user
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Get user profile to check role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'Perfil no encontrado' },
        { status: 404 }
      );
    }

    // Only allow admins to assign drivers
    if (profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'Solo los administradores pueden asignar repartidores' },
        { status: 403 }
      );
    }

    let requestBody;
    try {
      requestBody = await request.json();
    } catch {
      return NextResponse.json(
        { error: 'Formato de cuerpo de solicitud inválido' },
        { status: 400 }
      );
    }

    const { orderId, driverId } = requestBody;

    if (!orderId || !driverId) {
      return NextResponse.json(
        { error: 'Se requiere el ID de la orden y el ID del repartidor' },
        { status: 400 }
      );
    }

    // Validate that the driver exists and has the correct role
    const { data: driver, error: driverError } = await supabase
      .from('profiles')
      .select('id, role, full_name, is_active')
      .eq('id', driverId)
      .single();

    if (driverError || !driver) {
      return NextResponse.json(
        { error: 'Repartidor no encontrado' },
        { status: 404 }
      );
    }

    if (driver.role !== 'delivery') {
      return NextResponse.json(
        { error: 'El usuario seleccionado no es un repartidor' },
        { status: 400 }
      );
    }

    if (!driver.is_active) {
      return NextResponse.json(
        { error: 'El repartidor seleccionado no está activo' },
        { status: 400 }
      );
    }

    // Validate that the order exists and can be assigned
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('id, status, delivery_id')
      .eq('id', orderId)
      .single();

    if (orderError || !order) {
      return NextResponse.json(
        { error: 'Orden no encontrada' },
        { status: 404 }
      );
    }

    // Check if order is in a valid state for assignment
    if (order.status !== 'confirmed' && order.status !== 'pending') {
      return NextResponse.json(
        { 
          error: 'Solo se pueden asignar órdenes en estado "confirmado" o "pendiente"',
          currentStatus: order.status 
        },
        { status: 400 }
      );
    }

    // Check if order is already assigned
    if (order.delivery_id) {
      return NextResponse.json(
        { error: 'Esta orden ya tiene un repartidor asignado' },
        { status: 400 }
      );
    }

    // Assign the driver to the order
    const { error: updateError } = await supabase
      .from('orders')
      .update({
        delivery_id: driverId,
        status: 'confirmed', // Ensure status is confirmed when assigned
        updated_at: new Date().toISOString(),
      })
      .eq('id', orderId);

    if (updateError) {
      console.error('Error updating order:', updateError);
      return NextResponse.json(
        { error: 'Error al asignar el repartidor a la orden' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Repartidor ${driver.full_name} asignado exitosamente a la orden`,
      orderId,
      driverId,
      driverName: driver.full_name,
    });

  } catch (error) {
    console.error('Unexpected error in assign-driver API:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Driver assignment API endpoint is working',
    timestamp: new Date().toISOString(),
  });
}
