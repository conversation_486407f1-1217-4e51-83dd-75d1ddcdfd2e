import { NextResponse, type NextRequest } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/lib/supabase';

/**
 * Updates the Supabase session on every request and optionally enforces
 * authentication for protected paths.
 */
export async function updateSession(request: NextRequest) {
  const response = NextResponse.next({ request });

  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            response.cookies.set(name, value, {
              ...options,
              secure: process.env.NODE_ENV === 'production', // secure only in production
              sameSite: 'lax',
              path: '/',
              maxAge: 60 * 60 * 24 * 7, // 7 days
              // Domain is automatically handled - works for both localhost and production
            });
          });
        },
      },
    }
  );

  // Refresh session if expired - required for Server Components
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Define protected paths
  const protectedPaths = ['/admin', '/customer', '/dashboard'];
  const isProtectedPath = protectedPaths.some(path =>
    request.nextUrl.pathname.startsWith(path)
  );

  // Redirect unauthenticated users away from protected paths
  if (isProtectedPath && !user) {
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }

  // Redirect authenticated users away from auth pages (except password reset flows)
  const authPaths = ['/auth/login', '/auth/sign-up'];
  const isAuthPath = authPaths.some(path =>
    request.nextUrl.pathname.startsWith(path)
  );
  const isPasswordResetPath =
    request.nextUrl.pathname.startsWith('/auth/forgot-password') ||
    request.nextUrl.pathname.startsWith('/auth/confirm');

  if (isAuthPath && user && !isPasswordResetPath) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  return response;
}
