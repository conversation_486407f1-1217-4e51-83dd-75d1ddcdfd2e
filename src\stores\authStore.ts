import { create } from 'zustand';
import { createClient } from '@/utils/supabase/client';
import {
  User,
  Session,
  AuthError,
  AuthResponse,
  AuthTokenResponse,
} from '@supabase/supabase-js';

interface UserProfile {
  id: string;
  email: string;
  full_name: string | null;
  phone: string | null;
  role: 'customer' | 'admin' | 'delivery';
  created_at: string;
  updated_at: string;
}

interface AuthMetadata {
  [key: string]: string | number | boolean | null | undefined;
}

interface AuthState {
  // State
  user: User | null;
  session: Session | null;
  profile: UserProfile | null;
  loading: boolean;
  dbError: string | null;
  isInPasswordReset: boolean;

  // Computed values (now stored as state)
  isAdmin: boolean;
  isCustomer: boolean;
  isDelivery: boolean;
  hasValidMainAppRole: boolean;
  shouldRedirectToDelivery: boolean;
  hasValidRole: boolean;
  isInInvalidState: boolean;

  // Actions
  setUser: (user: User | null) => void;
  setSession: (session: Session | null) => void;
  setProfile: (profile: UserProfile | null) => void;
  setLoading: (loading: boolean) => void;
  setDbError: (error: string | null) => void;
  setIsInPasswordReset: (isInPasswordReset: boolean) => void;

  // Auth methods
  signIn: (email: string, password: string) => Promise<AuthTokenResponse>;
  signUp: (
    email: string,
    password: string,
    metadata?: Record<string, string>
  ) => Promise<AuthResponse>;
  signOut: () => Promise<{ error: AuthError | null }>;
  resetPassword: (
    email: string
  ) => Promise<{ data: object | null; error: AuthError | null }>;
  updatePassword: (
    password: string,
    additionalMetadata?: AuthMetadata
  ) => Promise<{ data: User | null; error: AuthError | null }>;
  completeProfile: (
    fullName: string,
    phone: string
  ) => Promise<{ error: AuthError | null }>;

  // Profile methods
  fetchProfile: (userId: string) => Promise<UserProfile | null>;
  refreshAuth: () => Promise<void>;

  // Initialization
  initialize: () => void;
}

const supabase = createClient();

// Helper function to compute role-based properties
const computeRoleProperties = (
  profile: UserProfile | null,
  user: User | null
) => {
  const isAdmin = profile?.role === 'admin';
  const isCustomer = profile?.role === 'customer';
  const isDelivery = profile?.role === 'delivery';
  const hasValidMainAppRole =
    profile?.role === 'admin' || profile?.role === 'customer';
  const shouldRedirectToDelivery = profile?.role === 'delivery';
  const hasValidRole =
    profile?.role === 'admin' ||
    profile?.role === 'customer' ||
    profile?.role === 'delivery';
  const isInInvalidState = Boolean(user && profile && !hasValidRole);

  return {
    isAdmin,
    isCustomer,
    isDelivery,
    hasValidMainAppRole,
    shouldRedirectToDelivery,
    hasValidRole,
    isInInvalidState,
  };
};

export const useAuthStore = create<AuthState>((set, get) => ({
  // Initial state
  user: null,
  session: null,
  profile: null,
  loading: true,
  dbError: null,
  isInPasswordReset: false,

  // Computed values (initialized)
  isAdmin: false,
  isCustomer: false,
  isDelivery: false,
  hasValidMainAppRole: false,
  shouldRedirectToDelivery: false,
  hasValidRole: false,
  isInInvalidState: false,

  // State setters
  setUser: user => {
    const { profile } = get();
    const roleProps = computeRoleProperties(profile, user);
    set({ user, ...roleProps });
  },
  setSession: session => set({ session }),
  setProfile: profile => {
    const { user } = get();
    const roleProps = computeRoleProperties(profile, user);
    set({ profile, ...roleProps });
  },
  setLoading: loading => set({ loading }),
  setDbError: dbError => set({ dbError }),
  setIsInPasswordReset: isInPasswordReset => set({ isInPasswordReset }),

  // Auth methods
  signIn: async (email, password) => {
    set({ loading: true });
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    set({ loading: false });
    return { data, error } as AuthTokenResponse;
  },

  signUp: async (email, password, metadata) => {
    set({ loading: true });
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
      },
    });
    set({ loading: false });
    return { data, error } as AuthResponse;
  },

  signOut: async () => {
    set({ loading: true });
    const { error } = await supabase.auth.signOut();
    set({ loading: false });
    return { error: error ?? null };
  },

  resetPassword: async email => {
    const baseUrl =
      typeof window !== 'undefined'
        ? window.location.origin
        : process.env.NEXT_PUBLIC_VERCEL_URL
          ? `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`
          : process.env.NEXT_PUBLIC_APP_URL
            ? process.env.NEXT_PUBLIC_APP_URL
            : 'http://localhost:3000';

    const redirectUrl = `${baseUrl}/auth/confirm`;

    if (process.env.NODE_ENV === 'development') {
      console.log('Password reset redirect URL:', redirectUrl);
    }

    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: redirectUrl,
    });

    return { data, error };
  },

  updatePassword: async (password, additionalMetadata) => {
    const { data: sessionData } = await supabase.auth.getSession();
    const { data, error } = await supabase.auth.updateUser({
      password,
      data: {
        password_reset_at: new Date().toISOString(),
        password_reset_method: 'user_initiated',
        password_reset_token_expires_at: sessionData.session?.expires_at,
        ...additionalMetadata,
      },
    });
    return { data: data?.user ?? null, error };
  },

  completeProfile: async (fullName, phone) => {
    const { user } = get();
    if (!user) return { error: { message: 'No user logged in' } as AuthError };

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: fullName,
          phone: phone,
          role: 'delivery',
        })
        .eq('id', user.id);

      if (!error) {
        await get().fetchProfile(user.id);
        console.log('Profile completed and role changed to delivery');
      }

      return { error: error as AuthError | null };
    } catch (error) {
      return { error: error as AuthError };
    }
  },

  fetchProfile: async userId => {
    try {
      console.log('Fetching profile for user:', userId);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        if (
          error.code === 'PGRST116' ||
          error.message?.includes('relation "profiles" does not exist')
        ) {
          set({
            dbError:
              'Database not set up. Please run the database setup script.',
          });
          if (process.env.NODE_ENV === 'development') {
            console.warn(
              'Database setup required. Please execute database-setup.sql in Supabase.'
            );
          }
          return null;
        }

        if (error.code === 'PGRST116') {
          // Create a profile if it doesn't exist
          console.log('Profile not found, attempting to create one');
          const { user } = get();
          if (!user || !user.email) {
            console.error('Cannot create profile: no user or email');
            return null;
          }

          const { data: newProfile, error: createError } = await supabase
            .from('profiles')
            .insert([
              {
                id: userId,
                email: user.email,
                role: 'customer', // Default role
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              },
            ])
            .select()
            .single();

          if (createError) {
            console.error('Error creating profile:', createError);
            set({
              dbError: `Failed to create profile: ${createError.message}`,
            });
            return null;
          }

          console.log('Created new profile:', newProfile);
          get().setProfile(newProfile as UserProfile);
          set({ dbError: null });
          return newProfile as UserProfile;
        }

        if (process.env.NODE_ENV === 'development') {
          console.error('Error fetching profile:', error);
        }
        set({ dbError: `Profile fetch error: ${error.message}` });
        return null;
      }

      set({ dbError: null });
      return data as UserProfile;
    } catch (error) {
      console.error('Error in fetchProfile:', error);
      set({ dbError: 'Failed to connect to database' });
      return null;
    }
  },

  refreshAuth: async () => {
    try {
      console.log('Refreshing auth state');
      const {
        data: { session },
      } = await supabase.auth.getSession();

      set({ session, user: session?.user ?? null });

      if (session?.user) {
        // Don't await profile fetch - do it in the background
        get()
          .fetchProfile(session.user.id)
          .then(userProfile => {
            get().setProfile(userProfile);
          });
      } else {
        get().setProfile(null);
      }
    } catch (err) {
      const error = err as Error;

      if (error.message.includes('Invalid Refresh Token')) {
        await supabase.auth.signOut();
        get().setUser(null);
        get().setProfile(null);
        set({ session: null });
      }

      console.error('Error refreshing auth:', error);
      set({ dbError: 'Failed to refresh authentication' });
    }
  },

  initialize: () => {
    // Prevent multiple initializations
    if (get().loading === false) {
      return;
    }

    console.log('Initializing auth store');
    const mounted = true;
    let timeoutId: NodeJS.Timeout;

    const getInitialSession = async () => {
      try {
        const sessionPromise = supabase.auth.getSession();
        const timeoutPromise = new Promise((_, reject) => {
          timeoutId = setTimeout(
            () => reject(new Error('Session timeout')),
            5000
          );
        });

        const result = await Promise.race([sessionPromise, timeoutPromise]);
        const {
          data: { session },
        } = result as { data: { session: Session | null } };
        clearTimeout(timeoutId);

        if (!mounted) return;

        get().setUser(session?.user ?? null);
        set({ session });

        if (session?.user) {
          console.log('Found session with user:', session.user.email);
          // Don't await profile fetch - do it in the background
          get()
            .fetchProfile(session.user.id)
            .then(userProfile => {
              if (mounted) {
                console.log('Setting profile:', userProfile);
                get().setProfile(userProfile);
              }
            });
        } else {
          get().setProfile(null);
        }

        set({ loading: false });
      } catch (error) {
        clearTimeout(timeoutId);
        if (mounted) {
          console.error('[AuthStore] Error getting session:', error);

          if (
            error instanceof Error &&
            error.message.includes('Session timeout')
          ) {
            get().setUser(null);
            get().setProfile(null);
            set({ session: null, dbError: null });
          } else {
            set({ dbError: 'Failed to initialize authentication' });
          }

          set({ loading: false });
        }
      }
    };

    // Set up auth state change listener first
    // Note: subscription is intentionally not cleaned up as this store persists for app lifetime
    supabase.auth.onAuthStateChange(async (event, session) => {
      if (!mounted) return;

      console.log('Auth state changed:', event);
      get().setUser(session?.user ?? null);
      set({ session });

      if (session?.user) {
        // Don't await profile fetch - do it in the background
        get()
          .fetchProfile(session.user.id)
          .then(userProfile => {
            if (mounted) {
              console.log('Setting profile after auth change:', userProfile);
              get().setProfile(userProfile);
            }
          });
      } else {
        get().setProfile(null);
        set({ dbError: null });
      }

      // Set loading to false immediately to avoid blocking the UI
      set({ loading: false });
    });

    // Then get initial session
    getInitialSession();
  },
}));
