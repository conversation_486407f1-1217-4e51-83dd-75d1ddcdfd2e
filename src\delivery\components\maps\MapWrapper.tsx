'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Importar el mapa dinámicamente solo en el cliente
const SafeDeliveryMap = dynamic(() => import('./SafeDeliveryMap'), {
  ssr: false,
  loading: () => (
    <div className='w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center'>
      <div className='text-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2'></div>
        <p className='text-sm text-gray-600'>Preparando mapa...</p>
      </div>
    </div>
  ),
});

interface MapWrapperProps {
  pickupAddress?:
    | {
        street?: string;
        number?: string;
        city?: string;
        state?: string;
        zip?: string;
        lat?: number;
        lng?: number;
        name?: string;
        coordinates?: { lat: number; lng: number } | [number, number];
      }
    | string;
  deliveryAddress?:
    | {
        street?: string;
        number?: string;
        city?: string;
        state?: string;
        zip?: string;
        lat?: number;
        lng?: number;
        name?: string;
        coordinates?: { lat: number; lng: number } | [number, number];
      }
    | string;
  className?: string;
}

export default function MapWrapper({
  pickupAddress,
  deliveryAddress,
  className,
}: MapWrapperProps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return (
      <div className={`w-full ${className}`}>
        <div className='bg-gray-100 rounded-lg flex items-center justify-center h-full'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2'></div>
            <p className='text-sm text-gray-600'>Inicializando mapa...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <SafeDeliveryMap
        pickupAddress={pickupAddress}
        deliveryAddress={deliveryAddress}
        className='w-full h-full'
      />
    </div>
  );
}
