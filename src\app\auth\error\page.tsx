'use client';

import { AuthLayout } from '@/components/auth/auth-layout';

export default function AuthErrorPage() {
  return (
    <AuthLayout>
      <div className='w-full max-w-md'>
        <div className='text-center mb-8'>
          <h1 className='text-3xl font-bold text-white mb-2'>Mouvers</h1>
          <p className='text-gray-300'>Error de autenticación</p>
        </div>

        <div className='bg-white rounded-lg p-6 shadow-lg'>
          <div className='text-center'>
            <div className='mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4'>
              <svg
                className='h-6 w-6 text-red-600'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'
                />
              </svg>
            </div>
            
            <h3 className='text-lg font-medium text-gray-900 mb-2'>
              Error de autenticación
            </h3>
            
            <p className='text-sm text-gray-600 mb-6'>
              Ha ocurrido un error durante el proceso de autenticación. Por favor, intenta nuevamente.
            </p>

            <div className='space-y-3'>
              <a
                href='/auth/login'
                className='w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors'
              >
                Ir al Login
              </a>
              
              <a
                href='/auth/forgot-password'
                className='w-full inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors'
              >
                Recuperar contraseña
              </a>
            </div>
          </div>
        </div>
      </div>
    </AuthLayout>
  );
}
