import { z } from 'zod';

// Type for validation errors
export type ValidationErrors = Record<string, string[]>;

// Function to format Zod errors into a more usable format
export function formatZodErrors(error: z.ZodError): ValidationErrors {
  const formattedErrors: ValidationErrors = {};

  error.issues.forEach((err: z.ZodIssue) => {
    const field = err.path.join('.');
    if (!formattedErrors[field]) {
      formattedErrors[field] = [];
    }
    formattedErrors[field].push(err.message);
  });

  return formattedErrors;
}

// Function to get the first error for a specific field
export function getFieldError(
  errors: ValidationErrors,
  field: string
): string | undefined {
  return errors[field]?.[0];
}

// Function to check if a field has errors
export function hasFieldError(
  errors: ValidationErrors,
  field: string
): boolean {
  return !!errors[field] && errors[field].length > 0;
}

// Function to validate data with a schema and return formatted errors
export function validateData<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: ValidationErrors } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: formatZodErrors(error) };
    }
    throw error;
  }
}

// Function to safely validate data without throwing
export function safeValidate<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: ValidationErrors } {
  const result = schema.safeParse(data);

  if (result.success) {
    return { success: true, data: result.data };
  } else {
    return { success: false, errors: formatZodErrors(result.error) };
  }
}

// Function to create a validation helper for forms
export function createValidationHelper<T>(schema: z.ZodSchema<T>) {
  return {
    validate: (data: unknown) => validateData(schema, data),
    safeValidate: (data: unknown) => safeValidate(schema, data),
    parse: (data: unknown) => schema.parse(data),
    safeParse: (data: unknown) => schema.safeParse(data),
  };
}
