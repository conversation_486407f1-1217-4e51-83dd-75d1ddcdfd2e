import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  const next = searchParams.get('next') ?? '/dashboard';

  if (code) {
    const supabase = await createClient();

    // Enhanced PKCE flow with JWT session management
    const { data: sessionData, error } =
      await supabase.auth.exchangeCodeForSession(code);

    if (!error && sessionData.session) {
      // Check if this is a password reset flow
      const type = searchParams.get('type');
      if (type === 'recovery') {
        // For password reset, pass additional context for enhanced security
        const resetParams = new URLSearchParams({
          code,
          type: 'recovery',
          // Include JWT context for enhanced validation
          session_id: sessionData.session.user?.id || '',
          aal: 'aal1', // Default Authenticator Assurance Level
          expires_at: sessionData.session.expires_at?.toString() || '',
        });

        return NextResponse.redirect(
          `${origin}/auth/confirm?${resetParams.toString()}`
        );
      }

      // Regular authentication flow
      return NextResponse.redirect(`${origin}${next}`);
    }

    // Log authentication errors for security monitoring
    if (error) {
      console.error('PKCE authentication error:', {
        error: error.message,
        code: code ? 'present' : 'missing',
        origin,
        timestamp: new Date().toISOString(),
      });
    }
  }

  // Return the user to an error page with instructions
  return NextResponse.redirect(`${origin}/auth/auth-code-error`);
}
