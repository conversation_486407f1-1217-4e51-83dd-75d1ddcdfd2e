'use client';

import { useAuthStore } from '@/stores/authStore';
import AuthContainer from '@/delivery/components/auth/AuthContainer';
import Dashboard from '@/delivery/components/dashboard/Dashboard';

export default function DeliveryPage() {
  const { user, loading } = useAuthStore();

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600'></div>
      </div>
    );
  }

  return <div>{user ? <Dashboard /> : <AuthContainer />}</div>;
}
