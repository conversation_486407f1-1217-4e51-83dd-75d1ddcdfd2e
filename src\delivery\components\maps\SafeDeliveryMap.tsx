'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { geocodeAddress, GeocodingResult } from '@/delivery/lib/geocoding';

// Importar DeliveryMap dinámicamente solo en el cliente
const DeliveryMap = dynamic(() => import('./DeliveryMap'), {
  ssr: false,
  loading: () => (
    <div className='w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center'>
      <div className='text-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2'></div>
        <p className='text-sm text-gray-600'>Cargando mapa...</p>
      </div>
    </div>
  ),
});

interface SafeDeliveryMapProps {
  pickupAddress?:
    | {
        street?: string;
        number?: string;
        city?: string;
        state?: string;
        zip?: string;
        lat?: number;
        lng?: number;
        name?: string;
        coordinates?: { lat: number; lng: number } | [number, number];
      }
    | string;
  deliveryAddress?:
    | {
        street?: string;
        number?: string;
        city?: string;
        state?: string;
        zip?: string;
        lat?: number;
        lng?: number;
        name?: string;
        coordinates?: { lat: number; lng: number } | [number, number];
      }
    | string;
  className?: string;
}

export default function SafeDeliveryMap({
  pickupAddress,
  deliveryAddress,
  className,
}: SafeDeliveryMapProps) {
  const [canRender, setCanRender] = useState(false);
  const [geocodedPickup, setGeocodedPickup] = useState<GeocodingResult | null>(
    null
  );
  const [geocodedDelivery, setGeocodedDelivery] =
    useState<GeocodingResult | null>(null);
  const [isGeocoding, setIsGeocoding] = useState(false);

  useEffect(() => {
    const geocodeAddresses = async () => {
      if (!pickupAddress || !deliveryAddress) return;

      setIsGeocoding(true);
      console.log('🔍 SafeDeliveryMap - Iniciando geocodificación...');

      try {
        // Extraer direcciones como texto
        const pickupText = formatAddressText(pickupAddress);
        const deliveryText = formatAddressText(deliveryAddress);

        console.log('📍 Direcciones a geocodificar:', {
          pickupText,
          deliveryText,
        });

        // Geocodificar ambas direcciones
        const [pickupResult, deliveryResult] = await Promise.all([
          geocodeAddress(pickupText),
          geocodeAddress(deliveryText),
        ]);

        setGeocodedPickup(pickupResult);
        setGeocodedDelivery(deliveryResult);

        // Verificar si ambas geocodificaciones fueron exitosas
        const bothSuccessful = pickupResult.success && deliveryResult.success;
        setCanRender(bothSuccessful);

        console.log('✅ Geocodificación completada:', {
          pickup: pickupResult,
          delivery: deliveryResult,
          canRender: bothSuccessful,
        });
      } catch (error) {
        console.error('❌ Error en geocodificación:', error);
        setCanRender(false);
      } finally {
        setIsGeocoding(false);
      }
    };

    geocodeAddresses();
  }, [pickupAddress, deliveryAddress]);

  // Función para formatear dirección como texto
  const formatAddressText = (address: unknown): string => {
    if (!address) return '';

    // Si ya es un string, usarlo directamente
    if (typeof address === 'string') return address;

    // Si es un objeto, construir el texto
    if (typeof address === 'object' && address !== null) {
      const addr = address as Record<string, unknown>;
      const parts = [
        addr.street,
        addr.number,
        addr.city,
        addr.state,
        addr.zip,
      ].filter(Boolean);

      return parts.join(', ');
    }

    return '';
  };

  if (isGeocoding) {
    return (
      <div className={`w-full ${className}`}>
        <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
          <div className='flex items-center space-x-2'>
            <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600'></div>
            <div>
              <h3 className='text-sm font-medium text-blue-800'>
                Obteniendo coordenadas...
              </h3>
              <p className='text-xs text-blue-700 mt-1'>
                Convirtiendo direcciones en coordenadas geográficas.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!canRender) {
    return (
      <div className={`w-full ${className}`}>
        <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>
          <div className='flex items-center space-x-2'>
            <span className='text-yellow-600'>⚠️</span>
            <div>
              <h3 className='text-sm font-medium text-yellow-800'>
                Mapa no disponible
              </h3>
              <p className='text-xs text-yellow-700 mt-1'>
                {geocodedPickup?.error ||
                  geocodedDelivery?.error ||
                  'No se pudieron obtener las coordenadas de las direcciones.'}
              </p>
              <button
                onClick={() => window.location.reload()}
                className='mt-2 text-xs text-blue-600 hover:text-blue-800 underline'
              >
                Reintentar
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <DeliveryMap
        pickupAddress={{
          lat: geocodedPickup!.lat,
          lng: geocodedPickup!.lng,
          name: geocodedPickup!.display_name,
        }}
        deliveryAddress={{
          lat: geocodedDelivery!.lat,
          lng: geocodedDelivery!.lng,
          name: geocodedDelivery!.display_name,
        }}
        className='w-full h-full'
      />
    </div>
  );
}
