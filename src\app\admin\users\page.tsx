'use client';

import { useEffect, useState, useCallback } from 'react';
import { createClient } from '@/utils/supabase/client';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { UsersManagement } from '@/components/admin/users-management';
import { useAuthStore } from '@/stores/authStore';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

// Create Supabase client once outside component to prevent recreation
const supabase = createClient();

interface User {
  id: string;
  email: string;
  full_name: string | null;
  role: 'customer' | 'delivery' | 'admin';
  phone: string | null;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export default function AdminUsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const { user, profile } = useAuthStore();
  const router = useRouter();

  const fetchUsers = useCallback(async () => {
    // Only fetch if user is admin
    if (!user || !profile || profile.role !== 'admin') {
      return;
    }

    try {
      setLoading(true);

      // Fetch all profiles with user information
      const { data, error } = await supabase
        .from('profiles')
        .select(
          `
          id,
          email,
          full_name,
          role,
          phone,
          created_at,
          updated_at,
          is_active
        `
        )
        .order('created_at', { ascending: false });

      if (error) {
        return;
      }

      setUsers(data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  }, [user, profile]);

  const updateUserRole = useCallback(
    async (userId: string, newRole: User['role']) => {
      try {
        const { error } = await supabase
          .from('profiles')
          .update({ role: newRole })
          .eq('id', userId);

        if (error) {
          return false;
        }

        // Refresh the users list
        await fetchUsers();
        return true;
      } catch {
        return false;
      }
    },
    [fetchUsers]
  );

  const toggleUserStatus = useCallback(
    async (userId: string, isActive: boolean) => {
      try {
        const { error } = await supabase
          .from('profiles')
          .update({ is_active: isActive })
          .eq('id', userId);

        if (error) {
          return false;
        }

        // Refresh the users list
        await fetchUsers();
        return true;
      } catch {
        return false;
      }
    },
    [fetchUsers]
  );

  // Only fetch users when user is admin and profile is loaded
  useEffect(() => {
    if (user && profile && profile.role === 'admin') {
      fetchUsers();
    }
  }, [user, profile, fetchUsers]);

  if (!user || !profile || profile.role !== 'admin') {
    return (
      <ProtectedRoute requireAdmin>
        <div>Access denied</div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requireAdmin>
      <div className='container mx-auto px-4 py-8'>
        <div className='mb-8'>
          <div className='flex items-center gap-4 mb-4'>
            <Button
              variant='outline'
              onClick={() => router.push('/admin/dashboard')}
              className='flex items-center gap-2'
            >
              <ArrowLeft className='w-4 h-4' />
              Volver al Dashboard
            </Button>
          </div>
          <h1 className='text-3xl font-bold text-gray-900'>
            Gestión de Usuarios
          </h1>
          <p className='text-gray-600 mt-2'>
            Administra todos los usuarios del sistema: clientes, repartidores y
            administradores
          </p>
        </div>

        <UsersManagement
          users={users}
          onUpdateRole={updateUserRole}
          onToggleStatus={toggleUserStatus}
          isLoading={loading}
        />
      </div>
    </ProtectedRoute>
  );
}
