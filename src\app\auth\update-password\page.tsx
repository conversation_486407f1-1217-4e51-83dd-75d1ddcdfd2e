'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { AuthLayout } from '@/components/auth/auth-layout';

export default function UpdatePasswordPage() {
  const { user, loading } = useAuthStore();
  const router = useRouter();
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-black'></div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (newPassword !== confirmPassword) {
      setError('Las contraseñas no coinciden');
      return;
    }

    if (newPassword.length < 6) {
      setError('La nueva contraseña debe tener al menos 6 caracteres');
      return;
    }

    setIsUpdating(true);

    try {
      // Here you would typically call your API to update the password
      // For now, we'll simulate a successful update
      await new Promise(resolve => setTimeout(resolve, 1000));

      setSuccess('Contraseña actualizada exitosamente');
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
    } catch {
      setError('Error al actualizar la contraseña');
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <AuthLayout>
      <div className='w-full max-w-md'>
        <div className='text-center mb-8'>
          <h1 className='text-3xl font-bold text-white mb-2'>Mouvers</h1>
          <p className='text-gray-300'>Actualiza tu contraseña</p>
        </div>

        <div className='bg-white rounded-lg p-6 shadow-lg'>
          <form onSubmit={handleSubmit} className='space-y-4'>
            {error && (
              <div className='bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded'>
                {error}
              </div>
            )}

            {success && (
              <div className='bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded'>
                {success}
              </div>
            )}

            <div>
              <label
                htmlFor='currentPassword'
                className='block text-sm font-medium text-gray-700 mb-1'
              >
                Contraseña actual
              </label>
              <input
                type='password'
                id='currentPassword'
                value={currentPassword}
                onChange={e => setCurrentPassword(e.target.value)}
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                required
              />
            </div>

            <div>
              <label
                htmlFor='newPassword'
                className='block text-sm font-medium text-gray-700 mb-1'
              >
                Nueva contraseña
              </label>
              <input
                type='password'
                id='newPassword'
                value={newPassword}
                onChange={e => setNewPassword(e.target.value)}
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                required
              />
            </div>

            <div>
              <label
                htmlFor='confirmPassword'
                className='block text-sm font-medium text-gray-700 mb-1'
              >
                Confirmar nueva contraseña
              </label>
              <input
                type='password'
                id='confirmPassword'
                value={confirmPassword}
                onChange={e => setConfirmPassword(e.target.value)}
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                required
              />
            </div>

            <button
              type='submit'
              disabled={isUpdating}
              className='w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'
            >
              {isUpdating ? 'Actualizando...' : 'Actualizar contraseña'}
            </button>
          </form>

          <div className='mt-4 text-center'>
            <a
              href='/dashboard'
              className='text-sm text-blue-600 hover:text-blue-500'
            >
              Volver al dashboard
            </a>
          </div>
        </div>
      </div>
    </AuthLayout>
  );
}
