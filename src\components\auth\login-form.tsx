'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/ui/button';
import { Input, PasswordInput } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ratelimit, rateLimitConfigs, keyGenerators } from '@/lib/rate-limit';

export function LoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [shouldRedirect, setShouldRedirect] = useState(false);
  const { signIn, profile, loading: authLoading } = useAuthStore();
  const router = useRouter();

  // Check rate limit before allowing login
  const checkRateLimit = () => {
    const emailKey = keyGenerators.email(email);
    const result = ratelimit.check(emailKey, rateLimitConfigs.login);

    if (!result.allowed) {
      const errorMessage = `Demasiados intentos de inicio de sesión. Intenta de nuevo en ${Math.ceil((result.resetTime! - Date.now()) / 60000)} minutos.`;
      setError(errorMessage);
      return false;
    }

    return true;
  };

  // Handle redirect after successful login
  useEffect(() => {
    if (shouldRedirect && !authLoading) {
      if (profile?.role === 'delivery') {
        console.log('Redirecting to delivery dashboard');
        router.push('/delivery');
      } else if (profile?.role === 'admin') {
        console.log('Redirecting to admin dashboard');
        router.push('/admin/dashboard');
      } else if (profile?.role === 'customer') {
        console.log('Redirecting to customer dashboard');
        router.push('/customer/dashboard');
      } else {
        console.log('Redirecting to main dashboard');
        router.push('/dashboard');
      }
      setShouldRedirect(false);
    }
  }, [shouldRedirect, profile, authLoading, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Check rate limit before attempting login
    if (!checkRateLimit()) {
      setLoading(false);
      return;
    }

    try {
      const { error } = await signIn(email, password);
      if (error) {
        // Provide more specific error messages
        if (error.message.includes('Invalid login credentials')) {
          setError('Credenciales incorrectas. Verifica tu email y contraseña.');
        } else if (error.message.includes('Email not confirmed')) {
          setError('Por favor confirma tu email antes de iniciar sesión.');
        } else if (error.message.includes('Too many requests')) {
          setError(
            'Demasiados intentos. Espera un momento antes de intentar de nuevo.'
          );
        } else {
          setError(error.message || 'Error al iniciar sesión');
        }
      } else {
        setShouldRedirect(true);
      }
    } catch {
      setError('Ocurrió un error inesperado. Intenta de nuevo.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className='space-y-6 bg-gradient-to-br from-blue-900/20 to-green-900/20 backdrop-blur-sm border border-blue-400/30 rounded-lg p-6 shadow-xl w-full max-w-md'
    >
      <div className='text-center mb-6'>
        <h2 className='text-2xl font-bold text-white'>Bienvenido de vuelta</h2>
        <p className='text-gray-300'>
          Accede a tu cuenta para gestionar entregas
        </p>
      </div>
      {error && (
        <div className='p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md'>
          {error}
        </div>
      )}

      <div className='space-y-2'>
        <Label htmlFor='email' className='text-white'>
          Correo electrónico
        </Label>
        <Input
          id='email'
          type='email'
          placeholder='<EMAIL>'
          value={email}
          onChange={e => setEmail(e.target.value)}
          required
          disabled={loading}
        />
      </div>

      <div className='space-y-2'>
        <Label htmlFor='password' className='text-white'>
          Contraseña
        </Label>
        <PasswordInput
          id='password'
          placeholder='••••••••'
          value={password}
          onChange={e => setPassword(e.target.value)}
          required
          disabled={loading}
        />
      </div>

      <Button
        type='submit'
        className='w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white border-0'
        disabled={loading}
      >
        {loading ? 'Iniciando sesión...' : 'Iniciar sesión'}
      </Button>

      <div className='text-center'>
        <Link
          href='/auth/forgot-password'
          className='text-sm text-gray-300 hover:text-green-300 hover:underline transition-colors'
        >
          ¿Olvidaste tu contraseña?
        </Link>
      </div>

      <div className='text-center'>
        <p className='text-sm text-white'>
          ¿No tienes cuenta?{' '}
          <Link
            href='/auth/sign-up'
            className='text-green-300 hover:text-blue-300 hover:underline font-medium transition-colors'
          >
            Regístrate aquí
          </Link>
        </p>
      </div>

      <div className='text-center pt-2 border-t border-gray-600/30'>
        <p className='text-sm text-gray-300 mb-2'>¿Eres repartidor?</p>
        <Link
          href='/delivery'
          className='inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 rounded-lg transition-all duration-200 hover:shadow-lg'
        >
          🚚 Acceder como Repartidor
        </Link>
      </div>
    </form>
  );
}
