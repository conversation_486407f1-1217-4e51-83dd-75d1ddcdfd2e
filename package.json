{"name": "mouvers", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "prettier": "prettier", "prettier:check": "prettier --check .", "prettier:write": "prettier --write .", "format": "prettier --write . && next lint --fix", "type-check": "tsc --noEmit", "quality-check": "pnpm prettier:check && pnpm lint && pnpm type-check", "pre-commit": "bash scripts/pre-commit.sh"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.2", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "leaflet": "^1.9.4", "lucide-react": "^0.523.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-leaflet": "^4.2.1", "stripe": "^14.0.0", "styled-jsx": "^5.1.7", "tailwind-merge": "^3.3.1", "zod": "^4.1.5", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/leaflet": "^1.9.8", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "prettier": "^3.3.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}