import { z } from 'zod';

// Address validation schema
export const addressSchema = z.object({
  street: z
    .string()
    .min(1, 'La dirección es requerida')
    .min(5, 'La dirección debe tener al menos 5 caracteres'),
  city: z
    .string()
    .min(1, 'La ciudad es requerida')
    .min(2, 'La ciudad debe tener al menos 2 caracteres'),
  state: z
    .string()
    .min(1, 'El estado es requerido')
    .min(2, 'El estado debe tener al menos 2 caracteres'),
  zip: z
    .string()
    .min(1, 'El código postal es requerido')
    .regex(/^\d{5}(-\d{4})?$/, 'Formato de código postal inválido'),
  phone: z
    .string()
    .min(1, 'El teléfono es requerido')
    .regex(/^\+?[\d\s\-\(\)]+$/, 'Formato de teléfono inválido'),
  contactName: z
    .string()
    .min(1, 'El nombre de contacto es requerido')
    .min(2, 'El nombre debe tener al menos 2 caracteres'),
});

export type AddressData = z.infer<typeof addressSchema>;

// Package details validation schema
export const packageDetailsSchema = z.object({
  weight: z
    .string()
    .min(1, 'El peso es requerido')
    .regex(/^\d+(\.\d+)?\s*(kg|lb|g|oz)?$/i, 'Formato de peso inválido'),
  dimensions: z
    .string()
    .min(1, 'Las dimensiones son requeridas')
    .regex(
      /^\d+x\d+x\d+\s*(cm|in|m|ft)?$/i,
      'Formato de dimensiones inválido (ej: 30x20x15cm)'
    ),
  contentDescription: z
    .string()
    .min(1, 'La descripción del contenido es requerida')
    .min(10, 'La descripción debe tener al menos 10 caracteres')
    .max(500, 'La descripción no puede exceder 500 caracteres'),
});

export type PackageDetailsData = z.infer<typeof packageDetailsSchema>;

// Order creation validation schema
export const orderCreateSchema = z.object({
  pickupAddress: addressSchema,
  deliveryAddresses: z
    .array(addressSchema)
    .min(1, 'Al menos una dirección de entrega es requerida'),
  packageDetails: packageDetailsSchema.optional(),
  totalCost: z
    .number()
    .min(0, 'El costo total debe ser mayor o igual a 0')
    .optional(),
  paymentMethod: z
    .enum(['wallet', 'stripe', 'mixed'] as const)
    .default('stripe'),
});

export type OrderCreateFormData = z.infer<typeof orderCreateSchema>;

// Order status update validation schema
export const orderStatusUpdateSchema = z.object({
  orderId: z.string().uuid('ID de orden inválido'),
  newStatus: z.enum([
    'pending',
    'confirmed',
    'in-transit',
    'pending-admin-confirmation',
    'delivered',
    'closed',
    'cancelled',
  ] as const),
});

export type OrderStatusUpdateData = z.infer<typeof orderStatusUpdateSchema>;

// Order filter validation schema
export const orderFilterSchema = z.object({
  status: z
    .enum([
      'pending',
      'confirmed',
      'in-transit',
      'pending-admin-confirmation',
      'delivered',
      'closed',
      'cancelled',
      'all',
    ])
    .optional(),
  dateFrom: z.string().datetime('Fecha de inicio inválida').optional(),
  dateTo: z.string().datetime('Fecha de fin inválida').optional(),
  customerId: z.string().uuid('ID de cliente inválido').optional(),
  deliveryId: z.string().uuid('ID de repartidor inválido').optional(),
});

export type OrderFilterData = z.infer<typeof orderFilterSchema>;
